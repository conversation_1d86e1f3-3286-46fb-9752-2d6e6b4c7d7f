version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80;https://+:443
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=cherish;Username=postgres;Password=postgres
      - Database__Provider=postgresql
    depends_on:
      - postgres
    networks:
      - cherish-network

  postgres:
    image: postgres:16
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_MULTIPLE_DATABASES=cherish,cherish_identity
    ports:
      - "5433:5433"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-multiple-postgres-databases.sh:/docker-entrypoint-initdb.d/init-multiple-postgres-databases.sh
    networks:
      - cherish-network

networks:
  cherish-network:
    driver: bridge

volumes:
  postgres-data:
